/**
 * Migration script to fix payment amounts stored in cents instead of dollars
 * This script converts amounts > 100 from cents to dollars
 */

const mongoose = require('mongoose');
const Payment = require('../models/Payment');
const config = require('../config/config');

async function fixPaymentAmounts() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.mongodb.uri);
    console.log('Connected to MongoDB');

    // Find all payments where amount is likely in cents (> 100)
    const paymentsToFix = await Payment.find({
      amount: { $gt: 100 }
    });

    console.log(`Found ${paymentsToFix.length} payments with amounts > 100 (likely in cents)`);

    let fixedCount = 0;
    
    for (const payment of paymentsToFix) {
      const originalAmount = payment.amount;
      const newAmount = originalAmount / 100;
      
      // Only fix if the original amount looks like it's in cents
      // (e.g., 9996 -> 99.96, but don't change 150.00 -> 1.50)
      if (originalAmount >= 1000 || (originalAmount > 100 && originalAmount % 100 !== 0)) {
        await Payment.findByIdAndUpdate(payment._id, {
          amount: newAmount
        });
        
        console.log(`Fixed payment ${payment._id}: ${originalAmount} -> ${newAmount.toFixed(2)}`);
        fixedCount++;
      } else {
        console.log(`Skipped payment ${payment._id}: ${originalAmount} (already in correct format)`);
      }
    }

    console.log(`\nFixed ${fixedCount} payment records`);
    console.log('Migration completed successfully');

  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  fixPaymentAmounts();
}

module.exports = fixPaymentAmounts;
